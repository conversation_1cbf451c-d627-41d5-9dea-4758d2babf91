// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2025-05-15',
  devtools: { enabled: true },

  // 同构渲染配置：首页SSR，其他页面CSR
  ssr: true,
  // Vite配置
  vite: {
    build: {
      target: 'esnext',
      // 生产环境移除注释
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: false, // 保留console，但通过pure_funcs移除特定函数
          drop_debugger: true, // 移除debugger
          pure_funcs: ['console.log', 'console.debug'], // 只移除log和debug，保留warn和error
        },
        format: {
          comments: false, // 移除注释
        },
      },
      // 代码分割优化
      rollupOptions: {
        output: {
          manualChunks: {
            // 将大型第三方库分离到单独的chunk
            'vendor-vue': ['vue', 'vue-router'],
            'vendor-pinia': ['pinia'],
            'vendor-utils': ['@vueuse/core', '@vueuse/nuxt'],
          },
        },
      },
    },
    // 开发环境保留注释，生产环境只移除部分console
    esbuild:
      process.env.NODE_ENV === 'production'
        ? {
            drop: ['debugger'], // 只移除debugger，保留console.warn和console.error
          }
        : undefined,
  },

  // 生产环境HTML压缩配置
  nitro: {
    // Amplify 部署配置 - 自动检测
    preset: process.env.AWS_APP_ID ? 'aws-amplify' : undefined,

    // 优化的缓存和路由规则
    routeRules: {
      // 首页使用SSR渲染，SEO优化 + 性能缓存（延长缓存时间）
      '/': {
        ssr: true,
        prerender: false,
        headers: {
          'Cache-Control': 'max-age=900, stale-while-revalidate=1800', // 15分钟缓存，30分钟stale
          'Vary': 'User-Agent, Accept-Language',
        },
      },

      // 法律合规页面使用SSR渲染，关闭预渲染避免运行时配置问题 + 长期缓存
      '/terms': {
        ssr: true,
        prerender: false,
        headers: {
          'Cache-Control': 'max-age=86400, stale-while-revalidate=172800',
        }, // 1天缓存
      },
      '/privacy': {
        ssr: true,
        prerender: false,
        headers: {
          'Cache-Control': 'max-age=86400, stale-while-revalidate=172800',
        },
      },
      '/complaints': {
        ssr: true,
        prerender: false,
        headers: {
          'Cache-Control': 'max-age=86400, stale-while-revalidate=172800',
        },
      },
      '/content-removal': {
        ssr: true,
        prerender: false,
        headers: {
          'Cache-Control': 'max-age=86400, stale-while-revalidate=172800',
        },
      },
      '/record-keeping': {
        ssr: true,
        prerender: false,
        headers: {
          'Cache-Control': 'max-age=86400, stale-while-revalidate=172800',
        },
      },

      // 用户页面使用CSR渲染（需要登录状态）
      '/user/**': { ssr: false },

      // 故事详情页面使用SSR渲染，SEO优化
      '/story/**': {
        ssr: true,
        headers: {
          'Cache-Control': 'max-age=1800, stale-while-revalidate=3600',
        }, // 30分钟缓存
      },

      // Chat4 落地页使用SSR渲染，SEO优化
      '/chat4-landing': {
        ssr: true,
        prerender: false,
        headers: {
          'Cache-Control': 'max-age=600, stale-while-revalidate=1200', // 10分钟缓存
          'Vary': 'User-Agent, Accept-Language',
        },
      },

      // 其他页面使用SPA模式，保持CSR体验
      '/stories/**': { ssr: false },
      '/profile/**': { ssr: false },
      '/chat/**': { ssr: false },
      '/settings/**': { ssr: false },

      // API路由优化缓存（延长缓存时间）
      '/api/homepage-optimized': {
        cors: true,
        headers: {
          'Cache-Control': 'max-age=900, stale-while-revalidate=1800', // 15分钟缓存
          'Vary': 'User-Agent, Authorization',
        },
      },
      '/api/stories-content': {
        cors: true,
        headers: {
          'Cache-Control': 'max-age=3600, stale-while-revalidate=7200', // 1小时缓存
        },
      },

      // Sitemap缓存策略
      '/reelsitemap.xml': {
        cors: true,
        headers: {
          'Content-Type': 'application/xml; charset=utf-8',
          'Cache-Control':
            'public, max-age=3600, s-maxage=7200, stale-while-revalidate=86400', // 1小时缓存，CDN 2小时，stale 24小时
          'Vary': 'Accept-Encoding',
        },
      },

      // 静态资源缓存优化
      '/_nuxt/**': {
        headers: {
          'Cache-Control': 'public, max-age=31536000, immutable', // 1年缓存，不可变
        },
      },
      '/assets/**': {
        headers: {
          'Cache-Control': 'public, max-age=31536000, immutable', // 1年缓存，不可变
        },
      },
      '/favicon.ico': {
        headers: {
          'Cache-Control': 'public, max-age=86400', // 1天缓存
        },
      },
      '/robots.txt': {
        headers: {
          'Cache-Control': 'public, max-age=86400', // 1天缓存
        },
      },

      '/api/**': {
        cors: true,
        headers: {
          'Cache-Control': 'max-age=300, stale-while-revalidate=600', // API默认5分钟缓存
        },
      },
    },

    // 压缩和性能优化
    compressPublicAssets: true,

    // 生产环境HTML压缩
    minify: process.env.NODE_ENV === 'production',

    // 存储配置（用于服务端缓存）
    storage: {
      redis: {
        driver: 'redis',
        // Redis配置将在生产环境中启用
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
        password: process.env.REDIS_PASSWORD,
        db: parseInt(process.env.REDIS_DB || '0'),
      },
    },
  },

  // CSS配置
  css: ['~/assets/css/main.css', '~/assets/css/chat4.less'],

  // 应用配置
  app: {
    head: {
      // 动态配置将在 app.vue 中设置
      meta: [
        { charset: 'utf-8' },
        {
          name: 'viewport',
          content:
            'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no',
        },
      ],
      script:
        process.env.NUXT_PUBLIC_VOLC_APP_ID &&
        process.env.NUXT_PUBLIC_APP_NAME?.toLowerCase().includes('reel')
          ? [
              {
                innerHTML: `
            // 此段代码的作用是将SDK主文件未加载完成时，业务代码中调用的API依次进行放到数组q中缓存，等待SDK主文件加载完成后，再取出，并执行。
            (function(win, export_obj) {
              win['LogAnalyticsObject'] = export_obj;
              if (!win[export_obj]) {
                var _collect = function() {
                  _collect.q.push(arguments);
                }
                _collect.q = _collect.q || [];
                win[export_obj] = _collect;
              }
              win[export_obj].l = +new Date();
            })(window, 'collectEvent');
          `,
                type: 'text/javascript',
              },
              {
                src: 'https://lf3-data.volccdn.com/obj/data-static/log-sdk/collect/5.0/collect-rangers-v5.2.6.js',
                async: true,
              },
            ]
          : [],
    },
  },

  // 运行时配置
  runtimeConfig: {
    // 服务端环境变量
    apiSecret: process.env.API_SECRET,
    public: {
      // 客户端环境变量
      apiBase:
        process.env.NUXT_PUBLIC_API_BASE ||
        'https://api-test.zhijianyuzhou.com',
      // CSR 应用 URL 配置
      csrAppUrl: process.env.NUXT_PUBLIC_CSR_APP_URL,
      // 部署环境配置
      deploymentEnv:
        process.env.NUXT_PUBLIC_DEPLOYMENT_ENV || process.env.DEPLOYMENT_ENV,
      websiteTitle:
        process.env.NUXT_PUBLIC_WEBSITE_TITLE ||
        process.env.VITE_WEBSITE_TITLE ||
        'ReelPlay',
      logoUrl:
        process.env.NUXT_PUBLIC_LOGO_URL ||
        process.env.VITE_LOGO_URL ||
        'https://static.reelplay.ai/static/images/logo/reelplay_logo.png',
      appName:
        process.env.NUXT_PUBLIC_APP_NAME ||
        process.env.VITE_APP_NAME ||
        'ReelPlay',
      stripePublicKey: process.env.NUXT_PUBLIC_STRIPE_PUBLIC_KEY,
      // 支付提供商调试变量
      paymentProvider:
        process.env.VITE_PAYMENT_PROVIDER ||
        process.env.NUXT_PUBLIC_PAYMENT_PROVIDER ||
        '',
      // 支持邮箱配置
      supportEmail: process.env.NUXT_PUBLIC_SUPPORT_EMAIL,
      // CDN 配置
      cdnUrl: process.env.NUXT_PUBLIC_CDN_URL,
      staticUrl: process.env.NUXT_PUBLIC_STATIC_URL,
      // 火山引擎配置
      volcAppId: process.env.NUXT_PUBLIC_VOLC_APP_ID,
    },
  },

  modules: [
    '@nuxt/eslint',
    '@nuxt/fonts',
    '@nuxt/icon',
    '@nuxt/image',
    '@pinia/nuxt',
    '@vueuse/nuxt',
    // 移除 @unlok-co/nuxt-stripe，使用 shared-payment 代替
    'nuxt-gtag',
    // 暂时移除 @nuxt/content、@nuxt/scripts 和 @nuxt/test-utils 以简化配置
  ],

  // 图片优化配置
  image: {
    // 启用现代图片格式
    format: ['webp', 'avif', 'jpeg'],
    // 图片质量优化
    quality: 80,
    // 响应式图片尺寸
    screens: {
      xs: 320,
      sm: 640,
      md: 768,
      lg: 1024,
      xl: 1280,
      xxl: 1536,
    },
    // 预设尺寸
    presets: {
      avatar: {
        modifiers: {
          format: 'webp',
          width: 50,
          height: 50,
          quality: 80,
        },
      },
      cover: {
        modifiers: {
          format: 'webp',
          width: 400,
          height: 600,
          quality: 85,
        },
      },
    },
    // 启用懒加载
    loading: 'lazy',
  },

  // 移除 Stripe 配置，使用 shared-payment 代替

  // 字体优化 - 使用 @nuxt/fonts
  fonts: {
    families: [
      {
        name: 'Work Sans',
        provider: 'google',
        weights: [400, 500, 600], // 减少字体权重，只保留常用的
        styles: ['normal'], // 只保留正常样式，减少加载
        display: 'swap', // 字体交换策略
        preload: true,
        fallbacks: ['system-ui', 'sans-serif'], // 添加回退字体
      },
    ],
    // 字体优化选项
    defaults: {
      weights: [400, 500, 600],
      styles: ['normal'],
      subsets: ['latin'],
    },
    // 启用字体预加载
    experimental: {
      processCSSVariables: true,
    },
  },

  // 图标优化
  icon: {
    size: '24px',
    class: 'icon',
    aliases: {
      heart: 'lucide:heart',
      play: 'lucide:play',
      user: 'lucide:user',
      menu: 'lucide:menu',
    },
  },

  // Google Analytics 配置
  gtag: {
    // 主要的 Google Analytics ID
    id:
      process.env.NUXT_PUBLIC_GA_ID ||
      (process.env.NUXT_PUBLIC_APP_NAME?.toLowerCase().includes('reel')
        ? 'G-934KVL4CM4' // ReelPlay GA
        : 'G-BCD38QPPKH'), // PlayShot GA
    // 配置多个 tracking ID：Google Analytics + Google Ads
    // tags: [
    //   // Google Ads ID (用于转换跟踪)
    //   'AW-17411847645',
    // ],
    // 配置选项
    config: {
      send_page_view: true,
      anonymize_ip: true,
      // 只在生产环境启用
      debug_mode: process.env.NODE_ENV !== 'production',
    },
  },
})
