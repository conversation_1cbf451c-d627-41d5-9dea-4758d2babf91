/**
 * 注册转换跟踪插件
 * 自动启动注册转换跟踪功能
 */

export default defineNuxtPlugin(() => {
  // 确保在客户端运行
  if (!import.meta.client) return

  const { startTracking } = useRegistrationTracking()

  // 等待 DOM 加载完成后启动跟踪
  onMounted(() => {
    // 延迟启动，确保用户状态已初始化
    setTimeout(() => {
      console.log('🎯 启动注册转换跟踪 (nuxt-gtag)...')
      startTracking()
    }, 1000)
  })

  // 也可以在路由变化时检查
  const router = useRouter()
  router.afterEach(() => {
    // 在路由变化后延迟检查，确保用户状态已更新
    setTimeout(() => {
      const { manualCheck } = useRegistrationTracking()
      manualCheck()
    }, 500)
  })
})
