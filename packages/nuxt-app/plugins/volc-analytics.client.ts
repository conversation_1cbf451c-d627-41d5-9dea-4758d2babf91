/**
 * 火山引擎统计分析客户端插件
 * 只在 ReelPlay 环境下配置火山引擎统计参数
 */

export default defineNuxtPlugin(() => {
  // 只在客户端运行，且只在 ReelPlay 环境下启用
  if (!import.meta.client) return

  const config = useRuntimeConfig()

  // 只在 ReelPlay 环境下启用火山引擎
  const isReelPlay = config.public.appName?.toLowerCase().includes('reel')
  if (!isReelPlay || !config.public.volcAppId) return

  // 火山引擎配置
  const volcConfig = {
    app_id: Number(config.public.volcAppId), // ReelPlay: 20005873
    channel_domain: 'https://gator.volces.com',
    log: true, // 开启日志
    autotrack: false, // 全埋点开关，false关闭
  }

  // 等待火山引擎主脚本加载完成并配置
  const configVolcAnalytics = () => {
    if (typeof window !== 'undefined' && (window as any).collectEvent) {
      // 检查是否是真正的collectEvent函数（不是缓存队列）
      if (
        typeof (window as any).collectEvent === 'function' &&
        !(window as any).collectEvent.q
      ) {
        console.log('🌋 火山引擎主脚本已加载')

        // 初始化火山引擎
        try {
          ;(window as any).collectEvent('init', volcConfig)
          console.log('🌋 火山引擎初始化成功', volcConfig)

          // 此处可添加设置uuid、设置公共属性等代码
          ;(window as any).collectEvent('start') // 通知SDK设置完毕，可以真正开始发送事件了
          return true
        } catch (error) {
          console.error('🌋 火山引擎初始化失败:', error)
          return false
        }
      } else {
        if (process.env.NODE_ENV === 'development') {
          console.log('🌋 火山引擎缓存队列已就绪，等待主脚本加载...')
        }
        return false
      }
    } else {
      if (process.env.NODE_ENV === 'development') {
        console.log('🌋 等待火山引擎脚本加载...')
      }
      return false
    }
  }

  // 轮询检查火山引擎是否加载完成
  let isConfigured = false
  const checkInterval = setInterval(() => {
    if (configVolcAnalytics()) {
      isConfigured = true
      clearInterval(checkInterval)
    }
  }, 200)

  // 5秒后停止检查
  setTimeout(() => {
    clearInterval(checkInterval)
    if (process.env.NODE_ENV === 'development' && !isConfigured) {
      console.warn('🔧 火山引擎加载检查超时，可能需要检查网络连接')
    }
  }, 5000)
})

// 类型声明
declare global {
  interface Window {
    collectEvent?: (eventType: string, eventData?: any) => void
  }
}
