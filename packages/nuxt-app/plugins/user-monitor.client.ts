/**
 * 用户信息监听插件 - 客户端插件
 * 在应用启动时自动开始监听用户信息变化
 */

export default defineNuxtPlugin(() => {
  // 延迟启动监听，确保用户状态已初始化
  setTimeout(() => {
    try {
      const monitor = useUserInfoMonitor()
      const { startMonitoring } = monitor

      startMonitoring()

      // 开发环境下挂载调试工具
      if (import.meta.client && import.meta.dev) {
        // @ts-ignore
        window.userMonitor = monitor
      }
    } catch (error) {
      console.error('❌ 用户信息监听器启动失败:', error)
    }
  }, 2000)
})
