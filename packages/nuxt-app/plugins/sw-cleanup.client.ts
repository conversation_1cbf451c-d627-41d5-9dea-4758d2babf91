/**
 * Service Worker 清理插件
 * 注册清理型 Service Worker 来替换旧的 CSR Service Worker
 */

export default defineNuxtPlugin(() => {
  // 只在客户端运行
  if (import.meta.client && 'serviceWorker' in navigator) {
    // 等待页面加载完成后再注册 SW
    window.addEventListener('load', async () => {
      try {
        console.log('🧹 开始注册清理型 Service Worker...')

        // 注册新的 Service Worker（会自动替换旧的）
        const registration = await navigator.serviceWorker.register('/sw.js', {
          scope: '/', // 确保覆盖整个域名范围
        })

        console.log('✅ 清理型 Service Worker 注册成功:', registration.scope)

        // 监听 Service Worker 更新
        registration.addEventListener('updatefound', () => {
          const newWorker = registration.installing
          if (newWorker) {
            console.log('🔄 发现新的 Service Worker，正在安装...')

            newWorker.addEventListener('statechange', () => {
              if (newWorker.state === 'installed') {
                if (navigator.serviceWorker.controller) {
                  console.log(
                    '🔄 新的 Service Worker 已安装，旧缓存正在清理...',
                  )
                } else {
                  console.log('✅ Service Worker 首次安装完成')
                }
              }
            })
          }
        })

        // 监听来自 Service Worker 的消息
        navigator.serviceWorker.addEventListener('message', (event) => {
          if (event.data) {
            if (event.data.type === 'SW_UPDATED') {
              console.log('📢 Service Worker 消息:', event.data.message)
            } else if (event.data.type === 'CACHE_CLEARED') {
              console.log('🧹 缓存清理完成:', event.data.message)
              // 可以在这里显示用户通知或执行其他操作
            }
          }
        })

        // 检查是否有等待中的 Service Worker
        if (registration.waiting) {
          console.log('⏳ 发现等待中的 Service Worker，立即激活...')
          registration.waiting.postMessage({ type: 'SKIP_WAITING' })
        }
      } catch (error) {
        console.error('❌ Service Worker 注册失败:', error)
      }
    })
  }
})
