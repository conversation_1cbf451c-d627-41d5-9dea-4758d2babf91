/**
 * Facebook Pixel 客户端插件
 * 自定义实现，参考 csr-app 的优化策略
 */

export default defineNuxtPlugin(() => {
  // 确保只在客户端运行
  if (import.meta.server) return

  const config = useRuntimeConfig()
  const pixelId = config.public.fbPixelId

  // 只有在配置了 Pixel ID 时才初始化
  if (!pixelId) {
    console.log('🔄 Facebook Pixel ID not configured, skipping initialization')
    return
  }

  console.log('🔄 Initializing Facebook Pixel:', pixelId)

  // 优化的 Facebook Pixel 加载策略，减少对 LCP 的影响
  const initializeFacebookPixel = () => {
    let loaded = false

    const loadFacebookPixel = () => {
      if (loaded) return
      loaded = true

      const loadScript = () => {
        // Facebook Pixel 初始化代码
        const initPixelScript = (function (
          f: any,
          b: any,
          e: any,
          v: any,
          n: any,
          t: any,
          s: any,
        ) {
          if (f.fbq) return
          n = f.fbq = function (...args: any[]) {
            if (n.callMethod) {
              n.callMethod(...args)
            } else {
              n.queue.push(args)
            }
          }
          if (!f._fbq) f._fbq = n
          n.push = n
          n.loaded = !0
          n.version = '2.0'
          n.queue = []
          t = b.createElement(e)
          t.async = !0
          t.src = v
          s = b.getElementsByTagName(e)[0]
          s.parentNode.insertBefore(t, s)
        })(
          window,
          document,
          'script',
          'https://connect.facebook.net/en_US/fbevents.js',
        )

        // 避免 ESLint 警告
        void initPixelScript

        // 初始化 Pixel
        window.fbq('init', pixelId)
        window.fbq('track', 'PageView')

        console.log('✅ Facebook Pixel initialized successfully:', pixelId)
      }

      // 使用 requestIdleCallback 优化性能
      if (window.requestIdleCallback) {
        window.requestIdleCallback(loadScript, { timeout: 5000 })
      } else {
        setTimeout(loadScript, 2000)
      }
    }

    // 监听用户交互，延迟加载广告脚本
    const events = [
      'mousedown',
      'mousemove',
      'keypress',
      'scroll',
      'touchstart',
      'click',
    ]
    const onUserInteraction = () => {
      setTimeout(loadFacebookPixel, 1000) // 交互后1秒再加载
      events.forEach((event) => {
        document.removeEventListener(event, onUserInteraction, true)
      })
    }

    events.forEach((event) => {
      document.addEventListener(event, onUserInteraction, true)
    })

    // 备用方案：5秒后自动加载
    setTimeout(loadFacebookPixel, 5000)
  }

  // 初始化 Facebook Pixel
  initializeFacebookPixel()

  return {
    provide: {
      fbq: () => window.fbq,
    },
  }
})

// 扩展全局类型定义
declare global {
  interface Window {
    fbq: any
    requestIdleCallback?: (
      callback: () => void,
      options?: { timeout: number },
    ) => void
  }
}
