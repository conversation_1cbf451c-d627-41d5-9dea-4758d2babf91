/**
 * Facebook Pixel 客户端插件
 * 确保 Facebook Pixel 在客户端正确初始化并提供全局访问
 */

export default defineNuxtPlugin(() => {
  // 确保只在客户端运行
  if (process.server) return

  // 等待 nuxt-meta-pixel 模块初始化完成
  const checkFbq = () => {
    return new Promise<void>((resolve) => {
      const maxAttempts = 50 // 最多等待 5 秒
      let attempts = 0

      const check = () => {
        if (window.fbq || attempts >= maxAttempts) {
          resolve()
          return
        }
        attempts++
        setTimeout(check, 100)
      }

      check()
    })
  }

  // 初始化完成后设置全局访问
  checkFbq().then(() => {
    if (window.fbq) {
      console.log('✅ Facebook Pixel initialized successfully')
      
      // 发送初始页面浏览事件（如果 nuxt-meta-pixel 没有自动发送）
      try {
        window.fbq('track', 'PageView')
      } catch (error) {
        console.warn('Facebook Pixel PageView tracking failed:', error)
      }
    } else {
      console.warn('⚠️ Facebook Pixel not available after initialization timeout')
    }
  })

  return {
    provide: {
      fbq: () => window.fbq
    }
  }
})

// 扩展全局类型定义
declare global {
  interface Window {
    fbq: any
  }
}
