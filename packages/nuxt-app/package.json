{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "build:staging": "dotenv -e .env.staging -- nuxt build", "build:production": "NODE_ENV=production DEPLOYMENT_ENV=production nuxt build", "build:prod:reelplay": "dotenv -e .env.prod.reelplay -- nuxt build", "build:prod:playshot": "dotenv -e .env.prod.playshot -- nuxt build", "upload:staging": "cd ../.. && chmod +x ./upload-monorepo.sh && ./upload-monorepo.sh staging myserver nuxt", "upload:production": "cd ../.. && chmod +x ./upload-monorepo.sh && ./upload-monorepo.sh production myserver nuxt", "deploy:staging": "chmod +x ./build-and-deploy.sh && ./build-and-deploy.sh staging", "deploy:production": "chmod +x ./build-and-deploy.sh && ./build-and-deploy.sh production", "deploy": "npm run deploy:staging", "deploy:old:staging": "cd ../.. && chmod +x ./deploy-nuxt-only.sh && ./deploy-nuxt-only.sh staging", "deploy:old:production": "cd ../.. && chmod +x ./deploy-nuxt-only.sh && ./deploy-nuxt-only.sh production", "dev": "nuxt dev --port 3000 --host 0.0.0.0", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "type:check": "nuxt typecheck", "lint": "eslint .", "lint:fix": "eslint . --fix", "clean": "rm -rf .nuxt .output dist node_modules/.cache"}, "dependencies": {"@nuxt/eslint": "1.4.1", "@nuxt/fonts": "0.11.4", "@nuxt/icon": "1.14.0", "@nuxt/image": "1.10.0", "@pinia/nuxt": "^0.11.1", "@stripe/stripe-js": "^4.0.0", "@unhead/vue": "^2.0.3", "@vueuse/nuxt": "^11.0.0", "eslint": "^9.0.0", "ismobilejs": "^1.1.1", "nuxt": "^3.17.5", "nuxt-meta-pixel": "^2.0.2", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.0", "shared-payment": "workspace:*", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@iconify-json/lucide": "^1.2.53", "@iconify-json/simple-icons": "^1.2.40", "dotenv-cli": "^8.0.0", "less": "^4.2.0", "turbo": "^2.5.4", "typescript": "5.9.0-beta", "vue-tsc": "^2.2.8"}}