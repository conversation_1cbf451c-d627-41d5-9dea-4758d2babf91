/**
 * Facebook Pixel 工具函数
 * 提供与 csr-app 一致的 Facebook Pixel 功能
 */

/**
 * 获取 Facebook Pixel 实例
 */
export const useFacebookPixel = () => {
  const { $fbq } = useNuxtApp()

  /**
   * 发送自定义 Facebook Pixel 事件
   * @param eventName 事件名称
   * @param parameters 事件参数
   */
  const trackEvent = (eventName: string, parameters: Record<string, any> = {}) => {
    if (process.client && $fbq) {
      $fbq('track', eventName, parameters)
    }
  }

  /**
   * 发送自定义 Facebook Pixel 事件
   * @param eventName 事件名称
   * @param parameters 事件参数
   */
  const trackCustomEvent = (eventName: string, parameters: Record<string, any> = {}) => {
    if (process.client && $fbq) {
      $fbq('trackCustom', eventName, parameters)
    }
  }

  /**
   * 发送单个像素的事件
   * @param pixelId 像素ID
   * @param eventName 事件名称
   * @param parameters 事件参数
   */
  const trackSingleEvent = (pixelId: string, eventName: string, parameters: Record<string, any> = {}) => {
    if (process.client && $fbq) {
      $fbq('trackSingle', pixelId, eventName, parameters)
    }
  }

  /**
   * 发送页面浏览事件
   */
  const trackPageView = () => {
    if (process.client && $fbq) {
      $fbq('track', 'PageView')
    }
  }

  /**
   * 发送用户注册事件
   */
  const trackCompleteRegistration = () => {
    trackEvent('CompleteRegistration')
  }

  /**
   * 发送购买事件
   * @param value 购买金额
   * @param currency 货币类型
   */
  const trackPurchase = (value: number, currency: string = 'USD') => {
    trackEvent('Purchase', { value, currency })
  }

  /**
   * 发送添加到购物车事件
   * @param value 商品价值
   * @param currency 货币类型
   */
  const trackAddToCart = (value: number, currency: string = 'USD') => {
    trackEvent('AddToCart', { value, currency })
  }

  /**
   * 发送开始结账事件
   * @param value 结账金额
   * @param currency 货币类型
   */
  const trackInitiateCheckout = (value: number, currency: string = 'USD') => {
    trackEvent('InitiateCheckout', { value, currency })
  }

  /**
   * 发送故事和角色付费成功事件（与 csr-app 保持一致）
   * @param storyId 故事ID
   * @param actorId 角色ID
   * @param amount 金额
   */
  const trackPaymentSuccessForStoryAndCharacter = (storyId?: string, actorId?: string, amount?: number) => {
    trackCustomEvent('PaymentSuccessForStoryAndCharacter', {
      storyId,
      actorId,
      amount
    })
  }

  /**
   * 发送用户游戏开始事件
   */
  const trackUserPlayedGame = () => {
    trackCustomEvent('UserPlayedGame')
  }

  /**
   * 发送用户点击封面事件
   */
  const trackUserClickCover = () => {
    trackCustomEvent('UserClickCover')
  }

  return {
    trackEvent,
    trackCustomEvent,
    trackSingleEvent,
    trackPageView,
    trackCompleteRegistration,
    trackPurchase,
    trackAddToCart,
    trackInitiateCheckout,
    trackPaymentSuccessForStoryAndCharacter,
    trackUserPlayedGame,
    trackUserClickCover,
  }
}
