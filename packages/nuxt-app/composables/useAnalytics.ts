/**
 * 统计分析配置和事件跟踪
 * 支持 Google Analytics 和火山引擎
 */

export const useAnalytics = () => {
  const { brandingConfig } = useBranding()

  // 根据品牌获取 GA ID
  const getGAId = () => {
    const appName = brandingConfig.value.appName.toLowerCase()
    return appName.includes('reel') ? 'G-7J3CFG8D6T' : 'G-BCD38QPPKH'
  }

  // 检查是否是 ReelPlay 环境
  const isReelPlay = () => {
    return brandingConfig.value.appName.toLowerCase().includes('reel')
  }

  // 页面浏览事件
  const trackPageView = (pagePath?: string) => {
    if (!import.meta.client) return

    const path = pagePath || window.location.pathname

    // Google Analytics
    if (window.gtag) {
      window.gtag('config', getGAId(), {
        page_path: path,
      })
    }

    // 火山引擎 (只在 ReelPlay 环境)
    if (isReelPlay() && window.collectEvent) {
      window.collectEvent('PageView', {
        page_path: path,
        page_title: document.title,
        referrer: document.referrer || 'direct',
      })
    }
  }

  // 自定义事件跟踪
  const trackEvent = (eventName: string, parameters?: Record<string, any>) => {
    if (!import.meta.client) return

    const eventData = {
      ...parameters,
      brand: brandingConfig.value.appName,
    }

    // Google Analytics
    if (window.gtag) {
      window.gtag('event', eventName, eventData)
    }

    // 火山引擎 (只在 ReelPlay 环境)
    if (isReelPlay() && window.collectEvent) {
      window.collectEvent(eventName, eventData)
    }
  }

  // 故事相关事件
  const trackStoryEvent = (
    action: string,
    storyId: string,
    storyTitle?: string,
  ) => {
    trackEvent('story_interaction', {
      action,
      story_id: storyId,
      story_title: storyTitle,
    })
  }

  // 用户行为事件
  const trackUserEvent = (action: string, details?: Record<string, any>) => {
    trackEvent('user_action', {
      action,
      ...details,
    })
  }

  // 支付相关事件
  const trackPaymentEvent = (
    action: string,
    amount?: number,
    currency?: string,
  ) => {
    trackEvent('payment', {
      action,
      value: amount,
      currency: currency || 'USD',
    })
  }

  return {
    getGAId,
    trackPageView,
    trackEvent,
    trackStoryEvent,
    trackUserEvent,
    trackPaymentEvent,
  }
}

// 类型声明
declare global {
  interface Window {
    gtag: (...args: any[]) => void
    collectEvent?: (eventType: string, eventData?: any) => void
  }
}
