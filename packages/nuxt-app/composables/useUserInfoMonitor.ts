/**
 * 用户信息监听器 - 用于测试用户状态变化
 * 监听用户信息的变化并输出详细日志
 */

import { watch } from 'vue'
import { useUserStore } from '~/stores/user'

export const useUserInfoMonitor = () => {
  const userStore = useUserStore()

  /**
   * 格式化用户信息用于日志输出
   */
  const formatUserInfo = (userInfo: any) => {
    if (!userInfo) return 'null'

    return {
      uuid: userInfo.uuid,
      name: userInfo.name,
      email: userInfo.email,
      role: userInfo.role,
      status: userInfo.status,
      create_time: userInfo.create_time,
      coins: userInfo.coins,
      level: userInfo.level,
      vip_level: userInfo.vip_level,
    }
  }

  /**
   * 检查是否为当天注册的正常用户
   */
  const isNewRegisteredUser = (userInfo: any): boolean => {
    if (!userInfo) return false

    // 检查用户状态是否为 normal（非游客）
    if (userInfo.status !== 'normal' || userInfo.role === 'guest') {
      return false
    }

    // 检查是否有创建时间
    if (!userInfo.create_time) return false

    try {
      // 解析创建时间
      const createTime = new Date(userInfo.create_time)
      const today = new Date()

      // 检查是否为当天注册
      const isToday =
        createTime.getFullYear() === today.getFullYear() &&
        createTime.getMonth() === today.getMonth() &&
        createTime.getDate() === today.getDate()

      return isToday
    } catch (error) {
      console.error('Error parsing create_time:', error)
      return false
    }
  }

  /**
   * 开始监听用户信息变化
   */
  const startMonitoring = (): void => {
    if (!import.meta.client) {
      return
    }

    // 监听用户信息变化
    watch(
      () => userStore.userInfo,
      (newUserInfo) => {
        if (newUserInfo) {
          const isNewUser = isNewRegisteredUser(newUserInfo)

          if (isNewUser) {
            // 触发注册转换跟踪
            try {
              const { triggerRegistrationConversion } =
                useRegistrationTracking()
              triggerRegistrationConversion(newUserInfo.uuid)
            } catch (error) {
              console.error('❌ 触发注册转换失败:', error)
            }
          }
        }
      },
      { deep: true, immediate: true },
    )
  }

  /**
   * 手动触发用户信息检查
   */
  const manualCheck = (): void => {
    if (userStore.userInfo) {
      const isNewUser = isNewRegisteredUser(userStore.userInfo)
      console.log('当天新注册用户:', isNewUser)
    }
  }

  /**
   * 模拟用户信息变化（测试用）
   */
  const simulateUserChange = (type: 'guest' | 'normal' | 'clear') => {
    switch (type) {
      case 'guest':
        userStore.setUserInfo({
          uuid: 'test-guest-' + Date.now(),
          name: 'Test Guest',
          role: 'guest',
          status: 'normal',
          create_time: new Date().toISOString(),
        })
        break

      case 'normal':
        userStore.setUserInfo({
          uuid: 'test-user-' + Date.now(),
          name: 'Test User',
          email: '<EMAIL>',
          role: 'normal',
          status: 'normal',
          create_time: new Date().toISOString(),
          coins: 100,
          level: 1,
        })
        break

      case 'clear':
        userStore.clearUserState()
        break
    }
  }

  return {
    startMonitoring,
    manualCheck,
    simulateUserChange,
    formatUserInfo,
    isNewRegisteredUser,
  }
}
