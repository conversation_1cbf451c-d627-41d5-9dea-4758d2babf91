/**
 * 故事数据管理 V2 - 重构版本
 * 使用新的TokenManager和DataManager实现智能数据获取
 */

import type { Story, Category } from './useApi'
import type { UserType } from './useTokenManager'

export const useStoriesV2 = () => {
  // 响应式状态
  const stories = ref<Story[]>([])
  const categories = ref<Category[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  const initialized = ref(false)

  // 筛选状态
  const selectedCategories = ref<string[]>([])
  const selectedSort = ref('popular')

  // 分页状态
  const currentPage = ref(1)
  const pageSize = ref(12)
  const total = ref(0)

  // 用户状态
  const currentUserType = ref<UserType>('guest')
  const dataSource = ref<'ssr' | 'cache' | 'api'>('api')

  // 管理器实例
  const { getOptimalStories, clearAllCache } = useDataManager()
  const { checkTokenStatus, getExistingToken } = useTokenManager()
  const { getEnhancedStories } = useStoriesEnhancer()
  const api = useApi()

  // 计算属性
  const filteredStories = computed(() => {
    // 如果需要前端额外筛选，可以在这里实现
    return stories.value
  })

  const hasMore = computed(() => {
    if (total.value === 0) return false
    return stories.value.length < total.value
  })

  const availableTags = computed(() => {
    const tags: { id: string; name: string }[] = []

    categories.value.forEach((category) => {
      if (category.subcategories) {
        category.subcategories.forEach((sub) => {
          tags.push({
            id: sub.id,
            name: sub.name,
          })
        })
      } else {
        tags.push({
          id: category.id,
          name: category.name,
        })
      }
    })

    return tags
  })

  /**
   * 使用缓存策略获取故事（新方法）
   */
  const fetchStoriesWithCache = async (
    params: {
      sort?: string
      page?: number
      page_size?: number
      category?: string
      forceRefresh?: boolean
    } = {},
  ) => {
    try {
      // 获取用户token
      const userToken = getExistingToken()

      // 使用增强的故事获取方法（缓存 + 用户状态）
      const result = await getEnhancedStories({
        sort: params.sort || selectedSort.value,
        page: params.page || currentPage.value,
        page_size: params.page_size || pageSize.value,
        category: params.category,
        userToken: userToken || undefined,
      })

      return {
        stories: result.stories,
        total: result.total,
        page: result.page,
        page_size: result.page_size,
        userType: userToken ? 'user' : ('guest' as UserType),
        source: result.source as 'ssr' | 'cache' | 'api',
      }
    } catch (error: any) {
      console.error('Failed to fetch stories with cache:', error)

      // 降级到原有方法
      return await getOptimalStories({
        sort: params.sort || selectedSort.value,
        page: params.page || currentPage.value,
        page_size: params.page_size || pageSize.value,
        category_ids: params.category ? [params.category] : undefined,
        forceRefresh: params.forceRefresh,
      })
    }
  }

  /**
   * 智能初始化（避免重复请求）
   */
  const initialize = async (forceRefresh = false) => {
    if (initialized.value && !forceRefresh) {
      return
    }

    try {
      loading.value = true
      error.value = null

      // 并行获取分类和故事数据
      const [storiesResult] = await Promise.all([
        forceRefresh
          ? getOptimalStories({
              sort: selectedSort.value,
              page: currentPage.value,
              page_size: pageSize.value,
              category_ids:
                selectedCategories.value.length > 0
                  ? selectedCategories.value
                  : undefined,
              forceRefresh,
            })
          : fetchStoriesWithCache({
              sort: selectedSort.value,
              page: currentPage.value,
              page_size: pageSize.value,
              category:
                selectedCategories.value.length > 0
                  ? selectedCategories.value[0]
                  : undefined,
            }),
      ])

      stories.value = storiesResult.stories as Story[]
      currentUserType.value = storiesResult.userType
      dataSource.value = storiesResult.source

      // 安全地设置 total 值
      if ('total' in storiesResult) {
        total.value = storiesResult.total
      } else {
        total.value = storiesResult.stories.length
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to initialize stories'
      console.error('Stories initialization error:', err)
    } finally {
      loading.value = false
      initialized.value = true
    }
  }

  /**
   * 获取故事列表（智能版本）
   */
  const fetchStories = async (refresh = false, forceRefresh = false) => {
    if (refresh) {
      currentPage.value = 1
    }

    try {
      loading.value = true
      error.value = null

      // 根据是否强制刷新选择获取方法
      const result = forceRefresh
        ? await getOptimalStories({
            sort: selectedSort.value,
            page: currentPage.value,
            page_size: pageSize.value,
            category_ids:
              selectedCategories.value.length > 0
                ? selectedCategories.value
                : undefined,
            forceRefresh,
          })
        : await fetchStoriesWithCache({
            sort: selectedSort.value,
            page: currentPage.value,
            page_size: pageSize.value,
            category:
              selectedCategories.value.length > 0
                ? selectedCategories.value[0]
                : undefined,
            forceRefresh,
          })

      if (refresh || currentPage.value === 1) {
        stories.value = result.stories as Story[]
      } else {
        // 分页加载时追加数据
        stories.value.push(...(result.stories as Story[]))
      }

      currentUserType.value = result.userType
      dataSource.value = result.source

      // 安全地更新分页状态
      if ('total' in result) {
        total.value = result.total
      } else if (result.stories.length < pageSize.value) {
        total.value = stories.value.length
      }
    } catch (err: any) {
      error.value = err.message || 'Network error'
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取分类列表
   */
  const fetchCategories = async () => {
    const response = await api.fetchCategories()

    if (response.code === '0') {
      categories.value = response.data.category || []
    }
  }

  /**
   * 设置选中的分类（内部方法，用于URL同步）
   */
  const setSelectedCategories = (categoryIds: string[]) => {
    selectedCategories.value = categoryIds
  }

  /**
   * 设置排序方式（内部方法，用于URL同步）
   */
  const setSelectedSort = (sort: string) => {
    selectedSort.value = sort
  }

  /**
   * 更新排序方式
   */
  const updateSort = async (sort: string) => {
    selectedSort.value = sort
    await fetchStories(true, false) // 不强制刷新，可能使用缓存
  }

  /**
   * 更新分类筛选
   */
  const updateCategories = async (categoryIds: string[]) => {
    selectedCategories.value = categoryIds
    await fetchStories(true, true) // 强制刷新，因为筛选条件变了
  }

  /**
   * 切换分类选择
   */
  const toggleCategory = async (categoryId: string) => {
    const index = selectedCategories.value.indexOf(categoryId)
    if (index === -1) {
      selectedCategories.value.push(categoryId)
    } else {
      selectedCategories.value.splice(index, 1)
    }
    await fetchStories(true, true) // 强制刷新
  }

  /**
   * 加载更多（分页）
   */
  const loadMore = async () => {
    if (loading.value || !hasMore.value) return

    currentPage.value += 1
    await fetchStories(false, false)
  }

  /**
   * 刷新数据
   */
  const refresh = async () => {
    await fetchStories(true, true) // 强制刷新
  }

  /**
   * 获取故事详情
   */
  const getStoryDetail = async (storyId: string) => {
    try {
      const response = await api.fetchStoryDetail(storyId)

      if (response.code === '0') {
        return response.data.story
      } else {
        throw new Error(response.message)
      }
    } catch (err: unknown) {
      console.error('获取故事详情失败:', err)
      throw err
    }
  }

  /**
   * 切换收藏状态
   */
  const toggleFavorite = async (storyId: string, currentStatus: boolean) => {
    try {
      const response = currentStatus
        ? await api.removeFavorite(storyId)
        : await api.addFavorite(storyId)

      if (response.code === '0') {
        return response.data
      } else {
        throw new Error(response.message)
      }
    } catch (err: unknown) {
      console.error('切换收藏状态失败:', err)
      throw err
    }
  }

  /**
   * 清除缓存并重新获取
   */
  const clearCacheAndRefresh = async () => {
    clearAllCache()
    initialized.value = false
    await initialize(true)
  }

  /**
   * 检查用户状态变化
   */
  const checkUserStatusChange = async () => {
    const tokenStatus = await checkTokenStatus()
    if (tokenStatus.userType !== currentUserType.value) {
      await clearCacheAndRefresh()
    }
  }

  return {
    // 状态
    stories: readonly(stories),
    categories: readonly(categories),
    loading: readonly(loading),
    error: readonly(error),
    initialized: readonly(initialized),

    // 筛选状态
    selectedSort: readonly(selectedSort),
    selectedCategories: readonly(selectedCategories),

    // 分页状态
    currentPage: readonly(currentPage),
    pageSize: readonly(pageSize),
    total: readonly(total),

    // 用户状态
    currentUserType: readonly(currentUserType),
    dataSource: readonly(dataSource),

    // 计算属性
    hasMore,
    filteredStories,
    availableTags,

    // 方法
    initialize,
    fetchStories,
    fetchCategories,
    getStoryDetail,
    toggleFavorite,
    updateSort,
    updateCategories,
    toggleCategory,
    loadMore,
    refresh,
    clearCacheAndRefresh,
    checkUserStatusChange,
    setSelectedCategories,
    setSelectedSort,
  }
}
