import { ref, watch } from 'vue'

/**
 * 标签筛选的Composable函数
 * 处理URL参数与标签筛选的逻辑
 */
export function useTagsFilter() {
  const storyStore = useStoryStore()
  const route = useRoute()
  const router = useRouter()

  // 加载状态标志
  const isLoading = ref(false)

  // 筛选状态
  const selectedPopular = ref('popular')
  const selectedGender = ref('')
  const selectedTags = ref<string[]>([])

  // 获取标签名称的辅助函数
  const getTagNameById = (tagId: string): string | null => {
    for (const category of storyStore.storyCategories) {
      // 搜索所有分类，不只是HOBBY
      if (category?.subcategories) {
        const tag = category.subcategories.find(
          (subcategory) => subcategory.id === tagId,
        )
        if (tag) {
          return tag.name
        }
      }
    }
    return null
  }

  // 获取标签ID的辅助函数
  // const getTagIdByName = (tagName: string): string | null => {
  //   for (const category of storyStore.storyCategories) {
  //     if (category?.name?.toUpperCase() === 'HOBBY' && category.subcategories) {
  //       const tag = category.subcategories.find(
  //         (subcategory) => subcategory.name.toLowerCase() === tagName.toLowerCase()
  //       )
  //       if (tag) {
  //         return tag.id
  //       }
  //     }
  //   }
  //   return null
  // }

  // 更新URL参数
  const updateUrlParams = () => {
    const params = new URLSearchParams(window.location.search)

    // 更新排序参数
    if (selectedPopular.value !== 'popular') {
      params.set('sort', selectedPopular.value)
    } else {
      params.delete('sort')
    }

    // 更新性别参数
    if (selectedGender.value) {
      params.set('gender', selectedGender.value)
    } else {
      params.delete('gender')
    }

    // 更新标签参数
    if (selectedTags.value.length > 0) {
      const tagNames = selectedTags.value
        .map((tagId) => getTagNameById(tagId))
        .filter((name) => name !== null) as string[]

      if (tagNames.length > 0) {
        params.set('tags', tagNames.join(','))
      } else {
        params.delete('tags')
      }
    } else {
      params.delete('tags')
    }

    // 更新URL，不刷新页面
    router.replace({ query: Object.fromEntries(params) })
  }

  // 获取性别名称的辅助函数 - 用于URL参数
  const _getGenderNameById = (genderId: string): string | null => {
    for (const category of storyStore.storyCategories) {
      if (
        category?.name?.toUpperCase() === 'GENDER' &&
        category.subcategories
      ) {
        const gender = category.subcategories.find(
          (subcategory) => subcategory.id === genderId,
        )
        if (gender) {
          return gender.name
        }
      }
    }
    return null
  }

  // 从URL参数初始化筛选条件
  const initFromUrlParams = async () => {
    // 如果已经在加载中，避免重复初始化
    if (isLoading.value) {
      console.log('useTagsFilter: Already initializing, skipping...')
      return
    }

    // 设置加载状态 - 确保在初始化期间始终显示加载状态
    isLoading.value = true

    try {
      // 确保分类数据存在（store内部会处理防重复）
      // await storyStore.fetchCategories()

      const query = route.query
      const hasUrlParams = Object.keys(query).length > 0

      // 获取排序参数
      const sortParam = query.sort as string
      if (sortParam && ['popular', 'newest', 'liked'].includes(sortParam)) {
        selectedPopular.value = sortParam
      }

      // 获取性别参数
      const genderParam = query.gender as string
      if (genderParam) {
        selectedGender.value = genderParam
      }

      // 获取标签参数
      const tagsParam = query.tags as string
      if (tagsParam) {
        try {
          const tagNames = tagsParam.includes(',')
            ? tagsParam.split(',')
            : [tagsParam]
          const allTags: { id: string; name: string }[] = []

          storyStore.storyCategories.forEach((category) => {
            if (category?.subcategories) {
              category.subcategories.forEach((subcategory) => {
                if (!allTags.some((tag) => tag.id === subcategory.id)) {
                  allTags.push({
                    id: subcategory.id,
                    name: subcategory.name,
                  })
                }
              })
            }
          })

          const validTagIds = tagNames
            .map((tagName) => {
              const tag = allTags.find(
                (t) => t.name.toLowerCase() === tagName.toLowerCase(),
              )
              return tag ? tag.id : null
            })
            .filter((id) => id !== null) as string[]

          selectedTags.value = validTagIds
        } catch (error) {
          console.error('Error parsing tags from URL:', error)
        }
      }

      // 只有在没有故事数据或参数变化时才请求
      const categoryIds = [selectedGender.value, ...selectedTags.value].filter(
        (id) => id !== '',
      )
      const shouldFetch =
        storyStore.stories.length === 0 ||
        !storyStore.lastFetchParams ||
        JSON.stringify(categoryIds) !==
          JSON.stringify(storyStore.lastFetchParams.categoryIds) ||
        selectedPopular.value !== storyStore.lastFetchParams.sort

      if (shouldFetch) {
        await storyStore.fetchStories(categoryIds, selectedPopular.value)
      }

      if (hasUrlParams) {
        updateUrlParams()
      }
    } catch (error) {
      console.error('Error initializing from URL params:', error)
    } finally {
      isLoading.value = false
    }
  }

  // 监听筛选条件变化
  let isInitializing = true

  watch(
    [
      () => selectedPopular.value,
      () => selectedGender.value,
      () => selectedTags.value,
    ],
    async () => {
      if (isInitializing) return

      // 设置加载状态
      isLoading.value = true

      try {
        const categoryIds = [
          selectedGender.value,
          ...selectedTags.value,
        ].filter((id) => id !== '')
        await storyStore.fetchStories(categoryIds, selectedPopular.value)
        updateUrlParams()
      } catch (error) {
        console.error('Error fetching stories during filter change:', error)
      } finally {
        // 确保加载状态被重置
        isLoading.value = false
      }
    },
  )

  // 计算属性：标签按钮文本
  const tagsButtonText = () => {
    if (isLoading.value) {
      return 'Tags'
    }
    return selectedTags.value.length > 0
      ? `Tags (${selectedTags.value.length})`
      : 'Tags'
  }

  // 计算属性：性别标签文本
  const selectedGenderLabel = () => {
    if (!selectedGender.value) return 'Gender: All'
    const category = storyStore.storyCategories
      .find((c) => c?.name?.toUpperCase() === 'GENDER')
      ?.subcategories?.find((c) => c.id === selectedGender.value)
    return category ? `Gender: ${category.name}` : 'Gender: All'
  }

  // 计算属性：排序方式标签文本
  const selectedPopularLabel = () => {
    switch (selectedPopular.value) {
      case 'popular':
        return 'Most popular'
      case 'newest':
        return 'Newest'
      case 'liked':
        return 'Most liked'
      default:
        return 'Most popular'
    }
  }

  // 处理筛选条件变化的函数
  const handlePopularChange = async (value: string) => {
    isLoading.value = true
    try {
      selectedPopular.value = value
    } catch (error) {
      console.error('Error handling popular change:', error)
      isLoading.value = false
    }
  }

  const handleGenderChange = async (value: string) => {
    isLoading.value = true
    try {
      selectedGender.value = value
    } catch (error) {
      console.error('Error handling gender change:', error)
      isLoading.value = false
    }
  }

  const handleTagsChange = async (tags: string[]) => {
    isLoading.value = true
    try {
      selectedTags.value = tags
    } catch (error) {
      console.error('Error handling tags change:', error)
      isLoading.value = false
    }
  }

  // 完成初始化
  setTimeout(() => {
    isInitializing = false
  }, 100)

  return {
    selectedPopular,
    selectedGender,
    selectedTags,
    selectedPopularLabel,
    selectedGenderLabel,
    tagsButtonText,
    handlePopularChange,
    handleGenderChange,
    handleTagsChange,
    initFromUrlParams,
    isLoading,
  }
}
