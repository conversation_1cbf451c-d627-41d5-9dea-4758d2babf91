<template>
  <div class="homepage-container">
    <!-- SEO H1 标题 - 分散放置在顶部 -->
    <h1
      v-if="currentFilters.hasFilters && currentFilters.selectedTags.length > 0"
      class="seo-hidden seo-tags-title"
    >
      The best fun "{{ currentFilters.selectedTags.join(' ') }}" Otome Games –
      Play Now for Free
    </h1>

    <!-- 主要内容 -->
    <AdaptiveLayout>
      <!-- PC端布局 -->
      <template #pc>
        <HomePagePC :stories="displayStories" />
      </template>

      <!-- 移动端布局 -->
      <template #mobile>
        <HomePageMobile />
      </template>
    </AdaptiveLayout>

    <!-- SEO H2 标题 - 分散放置在中部 -->
    <h2
      v-if="currentFilters.hasFilters && currentFilters.selectedTags.length > 0"
      class="seo-hidden seo-tags-romance"
    >
      choose your own romance {{ currentFilters.selectedTags.join(' ') }} style
    </h2>

    <!-- SEO H3 标题 - 分散放置在底部 -->
    <h3
      v-if="currentFilters.hasFilters && currentFilters.selectedTags.length > 0"
      class="seo-hidden seo-tags-trial"
    >
      Best online otome games "{{ currentFilters.selectedTags.join(' ') }}"
      style – Free Trial Available
    </h3>
  </div>
</template>

<script setup>
// 导入火山引擎工具
import { trackVolcEvent, trackVolcPageView } from '~/utils/volcAnalytics'

// 使用品牌配置
const { brandingConfig } = useBranding()
const brand = brandingConfig.value

// 从HomePagePC和HomePageMobile获取筛选状态
const route = useRoute()

// 初始化PC端筛选状态（避免在computed中调用）
let pcTagsFilter = null
if (import.meta.client) {
  try {
    pcTagsFilter = useTagsFilter()
  } catch (e) {
    console.error('Failed to initialize PC tags filter:', e)
    // 如果失败，保持为null
  }
}

// 解析当前的筛选参数
const currentFilters = computed(() => {
  const query = route.query

  // 处理tags参数：可能是字符串（逗号分隔）或数组
  let tags = []

  // 如果是客户端，尝试从PC端的useTagsFilter获取标签状态
  if (import.meta.client && pcTagsFilter) {
    try {
      if (pcTagsFilter.selectedTags.value.length > 0) {
        // 将PC端的标签ID转换为标签名称
        const pcTagNames = []
        for (const tagId of pcTagsFilter.selectedTags.value) {
          for (const category of storyStore.storyCategories || []) {
            if (category?.subcategories) {
              const foundTag = category.subcategories.find(
                (sub) => sub.id === tagId,
              )
              if (foundTag) {
                pcTagNames.push(foundTag.name)
                break
              }
            }
          }
        }
        tags = pcTagNames
      }
    } catch (e) {
      console.error('Failed to get tags from PC filter:', e)
      // 如果获取PC端状态失败，回退到URL参数解析
    }
  }

  // 如果没有从PC端获取到标签，从URL参数解析
  if (tags.length === 0 && query.tags) {
    if (typeof query.tags === 'string') {
      // 如果是字符串，按逗号分割
      tags = query.tags.split(',').filter((tag) => tag.trim())
    } else if (Array.isArray(query.tags)) {
      // 如果已经是数组
      tags = query.tags
    }
  }

  const sortType = query.sort || query.popular || 'popular'

  return {
    selectedTags: tags,
    sortType,
    hasFilters: !!(tags.length > 0 || sortType !== 'popular'),
  }
})

// 获取标签名称（需要从storyStore获取）
const storyStore = useStoryStore()

// SSR备用数据 - 空数组，强制使用真实API数据
const fallbackStories = []

// 使用 Nuxt 的 useState 确保 SSR/CSR 数据同步
const seoStories = useState('seo-stories', () => fallbackStories)
const seoError = ref(null)

// 立即初始化主题，避免闪烁
if (import.meta.client) {
  const savedTheme = localStorage.getItem('theme')
  const isDark = savedTheme ? savedTheme === 'dark' : true
  document.body.classList.toggle('light-theme', !isDark)
}

// 使用新的智能故事数据管理
const { stories, error, initialize, initialized } = useStoriesV2()

// 显示数据：story.list数据优先，SSR数据只是fallback
const displayStories = computed(() => {
  try {
    // 如果客户端已经初始化，优先使用客户端数据（即使为空）
    if (initialized.value) {
      return stories.value || []
    }

    // 如果客户端未初始化，使用SSR数据作为fallback
    return seoStories.value || []
  } catch (e) {
    console.error('Error in displayStories computed:', e)
    return []
  }
})

// 使用筛选SEO功能
const { generateFilterSeo } = useFilterSeo()

// 动态SEO配置
const dynamicSeo = computed(() => {
  // 安全地获取第一个故事的描述
  let firstStoryDescription = ''
  if (!displayStories?.value) return
  try {
    if (
      Array.isArray(displayStories.value) &&
      displayStories.value.length > 0
    ) {
      firstStoryDescription = displayStories.value[0]?.description || ''
    }
  } catch (e) {
    // 如果获取失败，使用空字符串
    console.error('Failed to get first story description:', e)
    firstStoryDescription = ''
  }

  return generateFilterSeo({
    selectedTags: currentFilters.value.selectedTags,
    tagNames: currentFilters.value.selectedTags,
    sortType: currentFilters.value.sortType,
    hasFilters: currentFilters.value.hasFilters,
    firstStoryDescription,
  })
})

// 应用动态SEO
useSeoMeta({
  title: computed(() => dynamicSeo.value.title),
  description: computed(() => dynamicSeo.value.description),
  keywords: computed(() => dynamicSeo.value.keywords),
  ogTitle: computed(() => dynamicSeo.value.title),
  ogDescription: computed(() => dynamicSeo.value.description),
  ogType: 'website',
  ogUrl: brand.siteUrl,
  ogImage: brand.ogImageUrl,
  twitterCard: 'summary_large_image',
  twitterTitle: computed(() => dynamicSeo.value.title),
  twitterDescription: computed(() => dynamicSeo.value.description),
  twitterImage: brand.ogImageUrl,
})

// 服务端渲染时使用缓存的故事数据
const queryParams = {
  sort: route.query.sort || 'popular',
  category: route.query.category,
  tags: route.query.tags,
}

const { data: cachedStoriesData } = await useFetch('/api/stories-content', {
  key: computed(() => {
    const sort = route.query.sort || 'popular'
    const category = route.query.category || 'all'
    const tags = route.query.tags || 'none'
    return `ssr-stories-cache:${sort}:${category}:${tags}`
  }),
  server: true,
  query: queryParams,
  default: () => ({
    stories: [],
    total: 0,
    page: 1,
    page_size: 100,
    source: 'fallback',
    timestamp: Date.now(),
  }),
  transform: (data) => {
    // 确保数据格式正确
    return {
      stories: data.stories || [],
      total: data.total || 0,
      page: data.page || 1,
      page_size: data.page_size || 100,
      source: data.source || 'api',
      timestamp: data.timestamp || Date.now(),
    }
  },
})

// 设置SEO故事数据
if (cachedStoriesData.value.stories.length > 0) {
  seoStories.value = cachedStoriesData.value.stories
} else {
  seoStories.value = fallbackStories
}

// 客户端挂载时智能初始化
onMounted(async () => {
  // 火山引擎上报：访问主页
  trackVolcEvent('VisitIndexPage', {
    page: 'index',
    page_url: window.location.href,
    referrer: document.referrer || 'direct',
    timestamp: Date.now(),
  })

  // 火山引擎上报：页面浏览
  trackVolcPageView('/')

  // 确保分类数据被加载（用于SEO标签名称解析）
  // 但不重复请求，store 内部会处理防重复逻辑
  // await storyStore.fetchCategories()

  // 如果已经有SSR数据，就使用它，避免重复的客户端请求
  if (seoStories.value.length > 0) {
    // 将SSR数据同步到store中，避免客户端重新请求
    storyStore.setStories(seoStories.value)

    // 同步客户端筛选状态到组件中
    if (Object.keys(route.query).length > 0) {
      await nextTick()
    }

    return
  }

  // 只有在没有SSR数据时才进行客户端初始化
  await initialize(false) // 不强制刷新，可能使用缓存或SSR数据
})

// 错误处理
watch(error, (newError) => {
  if (newError) {
    console.error('客户端数据加载错误:', newError)
  }
})

watch(seoError, (newError) => {
  if (newError) {
    console.error('SSR数据加载错误:', newError)
  }
})

// 简化的结构化数据
useHead({
  script: [
    {
      type: 'application/ld+json',
      children: JSON.stringify({
        '@context': 'https://schema.org',
        '@type': 'WebSite',
        'name': brand.websiteTitle,
        'description': brand.description,
        'url': brand.siteUrl,
      }),
    },
    {
      type: 'application/ld+json',
      children: JSON.stringify({
        '@context': 'https://schema.org',
        '@type': 'ItemList',
        'name': 'Featured Interactive Stories',
        'description': 'Collection of AI-powered interactive stories',
        'numberOfItems': 6,
        'itemListElement': fallbackStories.slice(0, 6).map((story, index) => ({
          '@type': 'ListItem',
          'position': index + 1,
          'item': {
            '@type': 'CreativeWork',
            'name': story.title,
            'description': story.description,
            'image': story.preview_url,
            'author': {
              '@type': 'Organization',
              'name': brand.websiteTitle,
            },
            'genre': story.tags,
            'interactionStatistic': {
              '@type': 'InteractionCounter',
              'interactionType': 'https://schema.org/LikeAction',
              'userInteractionCount': story.like_count,
            },
          },
        })),
      }),
    },
  ],
})
</script>

<style scoped>
/* SEO 隐藏标题 - 对用户不可见但对搜索引擎可见 */
.seo-hidden {
  position: absolute;
  left: -10000px;
  top: auto;
  width: 1px;
  height: 1px;
  overflow: hidden;
  margin: 0;
  padding: 0;
  border: 0;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
}

/* 分散的SEO标题样式 */
.seo-tags-title {
  font-size: 24px;
  font-weight: bold;
  color: #000;
}

.seo-tags-romance {
  font-size: 20px;
  font-weight: bold;
  color: #333;
}

.seo-tags-trial {
  font-size: 18px;
  font-weight: bold;
  color: #555;
}
</style>
