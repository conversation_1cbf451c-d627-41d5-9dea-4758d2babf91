/**
 * 注册转换跟踪 Composable (CSR 版本)
 * 使用 vue-gtag-next 简化 Google Ads 转换事件发送
 */

import { watch } from 'vue'
import { event } from 'vue-gtag'
import { useUserStore } from '@/store/user'

export const useRegistrationTracking = () => {
  const userStore = useUserStore()
  // const gtag = useGtag()

  // 本地存储键名
  const REPORTED_USERS_KEY = 'reported_registration_users'

  /**
   * 检查用户是否为当天注册的正常用户
   */
  const isNewRegisteredUser = (userInfo: any): boolean => {
    if (!userInfo) return false

    // 检查用户状态是否为 normal（非游客）
    if (userInfo.status !== 'normal' || userInfo.role === 'guest') {
      return false
    }

    // 检查是否有创建时间
    if (!userInfo.create_time) return false

    try {
      // 解析创建时间
      const createTime = new Date(userInfo.create_time)
      const today = new Date()

      // 检查是否为当天注册
      const isToday =
        createTime.getFullYear() === today.getFullYear() &&
        createTime.getMonth() === today.getMonth() &&
        createTime.getDate() === today.getDate()

      return isToday
    } catch (error) {
      console.error('Error parsing create_time:', error)
      return false
    }
  }

  /**
   * 检查用户是否已经上报过
   */
  const isUserReported = (userId: string): boolean => {
    try {
      const reportedUsers = JSON.parse(
        localStorage.getItem(REPORTED_USERS_KEY) || '[]',
      )
      return reportedUsers.includes(userId)
    } catch (error) {
      console.error('Error checking reported users:', error)
      return false
    }
  }

  /**
   * 标记用户为已上报
   */
  const markUserAsReported = (userId: string): void => {
    try {
      const reportedUsers = JSON.parse(
        localStorage.getItem(REPORTED_USERS_KEY) || '[]',
      )

      if (!reportedUsers.includes(userId)) {
        reportedUsers.push(userId)
        localStorage.setItem(REPORTED_USERS_KEY, JSON.stringify(reportedUsers))
      }
    } catch (error) {
      console.error('Error marking user as reported:', error)
    }
  }

  /**
   * 触发 Google Ads 注册转换事件
   * 使用 vue-gtag-next 的简化 API
   */
  const triggerRegistrationConversion = (userId: string): void => {
    try {
      console.log('🎯 触发注册转换事件 - 用户ID:', userId)
      event('user_signup', {})
      // 标记为已上报
      markUserAsReported(userId)

      console.log('✅ 注册转换事件已发送 (vue-gtag-next)')
    } catch (error) {
      console.error('❌ 发送注册转换事件失败:', error)
    }
  }

  /**
   * 检查并处理注册转换
   */
  const checkAndTrackRegistration = (): void => {
    const userInfo = userStore.userInfo

    if (!userInfo || !userInfo.uuid) return

    // 检查是否为新注册用户
    if (!isNewRegisteredUser(userInfo)) return

    // 检查是否已经上报过
    if (isUserReported(userInfo.uuid)) {
      console.log('ℹ️ 用户已上报过注册转换，跳过')
      return
    }

    // 触发转换事件
    triggerRegistrationConversion(userInfo.uuid)
  }

  /**
   * 开始监听用户状态变化
   */
  const startTracking = (): void => {
    // 立即检查当前用户状态
    checkAndTrackRegistration()

    // 监听用户信息变化
    watch(
      () => userStore.userInfo,
      (newUserInfo) => {
        if (newUserInfo) {
          checkAndTrackRegistration()
        }
      },
      { deep: true, immediate: true },
    )
  }

  /**
   * 手动触发检查（用于特定场景）
   */
  const manualCheck = (): void => {
    checkAndTrackRegistration()
  }

  /**
   * 清除已上报记录（调试用）
   */
  const clearReportedUsers = (): void => {
    localStorage.removeItem(REPORTED_USERS_KEY)
    console.log('🧹 已清除注册转换上报记录')
  }

  return {
    startTracking,
    manualCheck,
    clearReportedUsers,
    isNewRegisteredUser,
    isUserReported,
  }
}
