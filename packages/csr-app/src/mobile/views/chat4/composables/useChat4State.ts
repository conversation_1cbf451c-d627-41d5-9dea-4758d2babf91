import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useChatEventsStore } from '@/store/chat-events'
import { useChatUIStore } from '@/store/chat-ui'
import { useChatMessagesStore } from '@/store/chat-messages'
import { useChatResourcesStore } from '@/store/chat-resources'
import { useStoryStore } from '@/store/story'
import { useUserStore } from '@/store/user'
import { useRechargeStore } from '@/store/recharge'
import { useSkillStore } from '@/store/skill'
import { useChat4StateMachine } from '@/composables/useChat4StateMachine'

export function useChat4State() {
  const route = useRoute()
  const router = useRouter()

  // Store
  const chatEventsStore = useChatEventsStore()
  const chatUIStore = useChatUIStore()
  const chatMessagesStore = useChatMessagesStore()
  const chatResourcesStore = useChatResourcesStore()
  const storyStore = useStoryStore()
  const userStore = useUserStore()
  const rechargeStore = useRechargeStore()
  const skillStore = useSkillStore()

  // Chat4状态机
  const stateMachine = useChat4StateMachine()

  // 使用状态机的当前场景
  const currentScene = computed(() => stateMachine.currentScene.value)

  // State
  const contentReady = ref(false)
  const showAuthDrawer = ref(false)
  const showShareModal = ref(false)
  const showLeaveConfirmModal = ref(false)
  const showFriendRequestModal = ref(false)
  const showFriendRequestMessage = ref(false) // 好友请求消息显示状态
  const friendRequestAccepted = ref(false) // 是否已接受好友请求
  const showStreamEndedOverlay = ref(false) // 直播结束覆盖层显示状态
  const showGiftModal = ref(false)
  const showFavorabilityDrawer = ref(false) // 好感度抽屉显示状态
  const heartClickCount = ref(0)
  const viewerCount = ref(Math.floor(Math.random() * 10001) + 10000)

  // Constants
  const sayHiText = 'Say Hi'

  // Navigation Guards
  let nextCallback: any = null
  const lastHiddenTime = ref(0)
  const totalHiddenTime = ref(0)
  const startTime = ref(0)

  // 自定义页面可见性处理（用于统计聊天时长）
  const handleChat4VisibilityChange = () => {
    if (document.hidden) {
      // 页面隐藏时记录时间
      lastHiddenTime.value = Date.now()
    } else {
      // 页面显示时累计隐藏时间
      if (lastHiddenTime.value > 0) {
        totalHiddenTime.value += Date.now() - lastHiddenTime.value
        lastHiddenTime.value = 0
      }
    }
  }

  // 清理UI弹窗状态
  const clearUIPopupStates = () => {
    console.log('Clearing UI popup states')
    showFriendRequestMessage.value = false
    showFriendRequestModal.value = false
    // 注意：不清理 friendRequestAccepted，因为这个状态可能影响聊天按钮的显示逻辑
  }

  return {
    // Store
    route,
    router,
    chatEventsStore,
    chatUIStore,
    chatMessagesStore,
    chatResourcesStore,
    storyStore,
    userStore,
    rechargeStore,
    skillStore,

    // State Machine
    stateMachine,
    currentScene,

    // State
    contentReady,
    showAuthDrawer,
    showShareModal,
    showLeaveConfirmModal,
    showFriendRequestModal,
    showFriendRequestMessage,
    friendRequestAccepted,
    showStreamEndedOverlay,
    showGiftModal,
    showFavorabilityDrawer,
    heartClickCount,
    viewerCount,

    // Constants
    sayHiText,

    // Navigation
    nextCallback: {
      get: () => nextCallback,
      set: (value: any) => {
        nextCallback = value
      },
    },
    lastHiddenTime,
    totalHiddenTime,
    startTime,
    handleChat4VisibilityChange,
    clearUIPopupStates,
  }
}
